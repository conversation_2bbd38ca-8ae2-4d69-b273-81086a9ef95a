﻿using Gmi.ApplicationLayer.Providers;
using Gmi.DomainModelLayer.Entities;
using System.Reflection;

namespace Gmi.ApplicationLayer.Helpers
{
    public class NotificationProviderFactory(IEnumerable<Lazy<INotificationProvider>> notificationProviders) : INotificationProviderFactory
    {
        private readonly IEnumerable<Lazy<INotificationProvider>> _notificationProviders = notificationProviders;

        public INotificationProvider Create(NotificationType type)
        {
            var provider = _notificationProviders
                .Select(p => p.Value)
                .FirstOrDefault(p => p.GetType().GetField(nameof(INotificationProvider.HandledType), BindingFlags.Public | BindingFlags.Static)?.GetValue(null).Equals(type) ?? false);

            if (provider != null)
            {
                return provider;
            }

            throw new ArgumentException($"Unsupported notification type: {type}");
        }
    }
}
