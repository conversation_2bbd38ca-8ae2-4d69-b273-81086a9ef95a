﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gmi.ApplicationLayer.Exceptions
{
    public class ReferencedObjectDeletionException : Exception
    {
        private Type _entityType;
        private string _keyField;
        private object _keyValue;
        private ReferencedObjectDeletionException() { }
        public ReferencedObjectDeletionException(Type entityType, string keyField, object keyValue)
        {
            _entityType = entityType;
            _keyField = keyField;
            _keyValue = keyValue;
        }

        public override string Message => $"Cannot delete entity of type {_entityType.Name} with {_keyField}={_keyValue} because some entities are referencing it.";

    }
}
