﻿using System.Drawing;
using System.Globalization;
using System.Security.Cryptography.X509Certificates;
using System.Text.RegularExpressions;
using Gmi.ApplicationLayer.Models.Common;
using Spire.Pdf;
using Spire.Pdf.Graphics;
using Spire.Pdf.Security;

namespace Gmi.ApplicationLayer.Helpers
{
    public static class SignatureHelper
    {
        public static Stream SignPdf(Stream stream, X509Certificate2 clientCertificate)
        {
            using PdfDocument doc = new();
            doc.LoadFromStream(stream);
            PdfCertificate cert = new(clientCertificate);
            PdfPageBase page = doc.Pages[^1];
            float signatureWidth = 180;
            float signatureHeight = 80;
            float pageWidth = page.Size.Width;
            float pageHeight = page.Size.Height;
            float x = pageWidth - signatureWidth;
            float y = pageHeight - signatureHeight;
            PdfSignature signature = new(doc, page, cert, "Signature")
            {
                Bounds = new RectangleF(x, y, signatureWidth, signatureHeight)
            };
            PdfPen borderPen = new(Color.Black, 0.2f)
            {
                DashStyle = PdfDashStyle.Dash,
                DashPattern = [30, 30],
            };

            page.Canvas.DrawRectangle(borderPen, x, y, signatureWidth, signatureHeight);
            var subjectInfo = ParseSubjectInfo(signature.Certificate.Subject);
            var author = $"{subjectInfo.G} {subjectInfo.SN}";
            string signingDate = DateTime.UtcNow.ToString("dddd dd MMMM yyyy, HH:mm:ss \"GMT\"zzz", new CultureInfo("it-IT"));
            PdfFont labelFont = new(PdfFontFamily.Helvetica, 9f, PdfFontStyle.Bold | PdfFontStyle.Italic);
            PdfFont valueFont = new(PdfFontFamily.Helvetica, 10f, PdfFontStyle.Bold);
            PdfFont dateFont = new(PdfFontFamily.Helvetica, 7f, PdfFontStyle.Bold);
            PdfBrush labelBrush = new PdfSolidBrush(new PdfRGBColor(0, 102, 161));
            PdfBrush valueBrush = PdfBrushes.Black;
            float labelX = x + 2;
            float labelY = y + 2;
            var signerLabel = "Firmato digitalmente da/Signed by:";
            page.Canvas.DrawString(signerLabel, labelFont, labelBrush, labelX, labelY);
            labelY += labelFont.MeasureString(signerLabel).Height;
            page.Canvas.DrawString(author, valueFont, valueBrush, labelX, labelY);
            labelY += valueFont.MeasureString(author).Height + 10;
            var dateLabel = "In data/On date:";
            page.Canvas.DrawString(dateLabel, labelFont, labelBrush, labelX, labelY);
            labelY += labelFont.MeasureString(dateLabel).Height;
            page.Canvas.DrawString(signingDate, dateFont, valueBrush, labelX, labelY);
            signature.DocumentPermissions = PdfCertificationFlags.ForbidChanges | PdfCertificationFlags.AllowFormFill;
            MemoryStream memoryStream = new();
            doc.SaveToStream(memoryStream);
            memoryStream.Seek(0, SeekOrigin.Begin);
            return memoryStream;
        }

        private static CertificateSubjectInfo ParseSubjectInfo(string subject)
        {
            string pattern = @"(?<key>\w+)=((\""(?<value>[^\""]+)\"")|(?<value>[^,]+))";
            Regex regex = new(pattern);

            var fields = new Dictionary<string, string>();

            foreach (Match match in regex.Matches(subject))
            {
                string key = match.Groups["key"].Value;
                string value = match.Groups["value"].Value;
                fields[key] = value.Trim();
            }
            var subjectInfo = new CertificateSubjectInfo
            {
                DnQualifier = fields.TryGetValue("dnQualifier", out var dnvalue) ? dnvalue : null,
                CN = fields.TryGetValue("CN", out var cnvalue) ? cnvalue : null,
                G = fields.TryGetValue("G", out var gvalue) ? gvalue : null,
                SN = fields.TryGetValue("SN", out var snvalue) ? snvalue : null,
                OU = fields.TryGetValue("OU", out var ouvalue) ? ouvalue : null,
                O = fields.TryGetValue("O", out var ovalue) ? ovalue : null,
                C = fields.TryGetValue("C", out var cvalue) ? cvalue : null,
            };

            return subjectInfo;
        }
    }

}