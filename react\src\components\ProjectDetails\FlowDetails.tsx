import React, { useEffect, useState } from 'react';
import { FlowDto, FlowAttributeCreateDto, FlowTypeSheetDto, FlowTypeAttributeDto } from '../../api/models';
import styles from './FlowDetails.module.css';
import { Button } from '../Buttons/Button';
import { formatDate } from '../../utils/dateUtils';
import { FlowTypeService } from '../../api/services/FlowTypeService';
import { SheetService } from '../../api/services/SheetService';
import { FlowService } from '../../api/services/FlowService';
import { FlowUserService } from '../../api/services/FlowUserService';
import { 
  HiOutlineClock, 
  HiOutlineDocumentText, 
  HiOutlineUsers,
  HiOutlineLightBulb,
  HiOutlinePaperClip,
  HiOutlineDocumentDuplicate,
  HiOutlineClipboardList,
  HiOutlineTag,
  HiOutlineOfficeBuilding
} from 'react-icons/hi';
import { CoordinationModal } from './CoordinationModal';
import { ContributorsModal } from './ContributorsModal';
import { SpeakModal } from './SpeakModal';
import { TextEditor } from '../TextEditor/TextEditor';
import { AttributeType, FlowStatus } from '../../api/enums';
import { Tooltip } from '../Tooltip/Tooltip';
import appStyles from '../../App.module.css';
import { ConfirmDialog } from '../Modal/ConfirmDialog';
import { toast } from 'react-toastify';
import { useFlowPermissions } from '../../hooks/useFlowPermissions';
import { useUserData } from '../../auth/AuthContext';
import { UserAction } from '../../models/UserActions';
import { getFlowStatusLabel, getFlowStatusStyles } from '../../utils/flowStatusUtils';

// Interfaccia per rappresentare un Sheet semplificato
interface FlowSheet {
  id: string;
  name: string;
  flowTypeSheetId: string;
  content?: string;
}

interface FlowDetailsProps {
  document: FlowDto;
  attributes: FlowAttributeCreateDto[];
  onCoordinate: () => void;
  onHistory: () => void;
  onRefresh?: () => void;
}

export const FlowDetails: React.FC<FlowDetailsProps> = ({ document, attributes, onHistory, onRefresh }) => {
  const [flowTypeSheets, setFlowTypeSheets] = useState<FlowTypeSheetDto[]>([]);
  const [flowSheets, setFlowSheets] = useState<FlowSheet[]>([]);
  const [flowTypeAttributes, setFlowTypeAttributes] = useState<FlowTypeAttributeDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCoordinationModal, setShowCoordinationModal] = useState(false);
  const [showContributorsModal, setShowContributorsModal] = useState(false);
  const [showSpeakModal, setShowSpeakModal] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [showPublishConfirm, setShowPublishConfirm] = useState(false);
  const [isSendingForPreApproval, setIsSendingForPreApproval] = useState(false);
  const [showSendForPreApprovalConfirm, setShowSendForPreApprovalConfirm] = useState(false);
  const [isAcknowledgingContribution, setIsAcknowledgingContribution] = useState(false);
  const [showAcknowledgeContributionConfirm, setShowAcknowledgeContributionConfirm] = useState(false);
  const [currentUserAcknowledged, setCurrentUserAcknowledged] = useState<boolean | null>(null);

  // Hook per gestire i permessi del flusso
  const { hasAction } = useFlowPermissions({
    flowId: document.id || ''
  });

  // Hook per ottenere i dati dell'utente corrente
  const { user: currentUser } = useUserData();

  // Utilizziamo direttamente gli attributi ricevuti come props
  const creatorInfo = {
    organizationUnit: document.organizationUnitName || 'N/A',
    name: document.creatorFullName || 'N/A',
    rank: document.creatorRank
  };

  // Create a mapping of attributes with their names from flowTypeAttributes
  // Show all flow type attributes, with real values when available
  const attributesWithNames = flowTypeAttributes.map(typeAttr => {
    const flowAttr = attributes.find(attr => attr.flowTypeAttributeId === typeAttr.id);
    let value = flowAttr?.value || 'Non specificato';
    
    // Format date values if the attribute type is DateTime
    if (typeAttr.attributeType === AttributeType._1 && flowAttr?.value) {
      try {
        const date = new Date(flowAttr.value);
        if (!isNaN(date.getTime())) {
          value = formatDate(date);
        }
      } catch (error) {
        console.error('Error formatting date:', error);
        // Keep the original value if formatting fails
      }
    }
    
    return {
      name: typeAttr.name || 'Attributo sconosciuto',
      value: value
    };
  });

  // Calcola i permessi per il TextEditor
  const canEditContent = hasAction(UserAction.SAVE_SECTIONS);
  const canComment = hasAction(UserAction.COMMENT);

  useEffect(() => {
    if (document.flowTypeId) {
      loadFlowTypeData();
    }
  }, [document.flowTypeId]);

  useEffect(() => {
    if (document.id && currentUser?.id) {
      checkCurrentUserAcknowledgeStatus();
    }
  }, [document.id, currentUser?.id]);

  const checkCurrentUserAcknowledgeStatus = async () => {
    try {
      if (!currentUser?.id) {
        setCurrentUserAcknowledged(null);
        return;
      }

      // Carica tutti i FlowUser per questo flusso
      const flowUsers = await FlowUserService.getByFlow(document.id!);
      
      // Trova il FlowUser dell'utente corrente
      const currentUserFlowRole = flowUsers.find((fu: any) => fu.userId === currentUser.id);
      
      if (currentUserFlowRole) {
        // Se l'utente è un contributore, controlla lo stato di acknowledge
        setCurrentUserAcknowledged(currentUserFlowRole.acknowledge === true);
      } else {
        // L'utente non è un contributore di questo flusso
        setCurrentUserAcknowledged(null);
      }
      
    } catch (error) {
      console.error('Error checking acknowledge status:', error);
      setCurrentUserAcknowledged(null);
    }
  };
  
  const loadFlowTypeData = async () => {
    try {
      setLoading(true);
      // Carica sia gli sheets che gli attributi in parallelo
      const [sheets, typeAttributes, flowSheets] = await Promise.all([
        FlowTypeService.getSheetsByFlowTypeId(document.flowTypeId!),
        FlowTypeService.getAttributesByFlowTypeId(document.flowTypeId!),
        SheetService.getByFlowId(document.id!)
      ]);
      
      // Sort sheets by order
      const sortedSheets = [...sheets].sort((a, b) => (a.order || 0) - (b.order || 0));
      setFlowTypeSheets(sortedSheets);
      
      // Mappa gli sheet con il contenuto
      const sheetsWithContent = flowSheets.map(sheet => ({
        id: sheet.id || '',
        name: sheet.name || '',
        flowTypeSheetId: sheet.flowTypeSheetId || '',
        content: sheet.content || ''
      }));
      setFlowSheets(sheetsWithContent);
      
      // Sort attributes by order
      const sortedAttributes = [...typeAttributes].sort((a, b) => (a.order || 0) - (b.order || 0));
      setFlowTypeAttributes(sortedAttributes);
      
    } catch (error) {
      console.error('Error loading flow type data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get sheet icon based on name
  const getSheetIcon = () => {
    return <HiOutlineDocumentText className={styles.sectionIcon} />;
  };

  const handleViewDetail = (departmentId: string) => {
    console.log('View detail for department:', departmentId);
    // In un'applicazione reale, qui potremmo caricare i dettagli specifici del dipartimento
  };

  const handleSpeakSubmit = (hasComments: boolean, comments: string) => {
    console.log('Speak submit with comments:', hasComments, comments);
    // Qui chiamerebbero le API per inviare il messaggio
  };

  const handlePublishDraft = async () => {
    // Controllo aggiuntivo lato client (il controllo principale è sul server)
    if (!hasAction(UserAction.PUBLISH_DRAFT)) {
      toast.error('Non hai i permessi necessari per pubblicare questa bozza.', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    try {
      setIsPublishing(true);
      await FlowService.publishDraft(document.id!);
      toast.success('Bozza pubblicata con successo!', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      if (onRefresh) {
        onRefresh();
      }
    } catch (error: any) {
      console.error('Errore durante la pubblicazione della bozza:', error);
      
      // Gestione specifica per errori di autorizzazione
      if (error?.response?.status === 403) {
        toast.error('Non hai i permessi necessari per pubblicare questa bozza.', {
          position: 'bottom-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      } else {
        toast.error('Si è verificato un errore durante la pubblicazione della bozza. Riprova più tardi.', {
          position: 'bottom-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      }
    } finally {
      setIsPublishing(false);
    }
  };

  const handleSendForPreApproval = async () => {
    // Controllo aggiuntivo lato client (il controllo principale è sul server)
    if (!hasAction(UserAction.SEND_FOR_PRE_APPROVAL)) {
      toast.error('Non hai i permessi necessari per inviare in pre-approvazione.', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    try {
      setIsSendingForPreApproval(true);
      await FlowService.sendForPreApproval(document.id!);
      
      toast.success('Documento inviato in pre-approvazione con successo!', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      if (onRefresh) {
        onRefresh();
      }
    } catch (error: any) {
      console.error('Errore durante l\'invio in pre-approvazione:', error);
      
      // Gestione specifica per errori di autorizzazione
      if (error?.response?.status === 403) {
        toast.error('Non hai i permessi necessari per inviare in pre-approvazione.', {
          position: 'bottom-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      } else {
        toast.error('Si è verificato un errore durante l\'invio in pre-approvazione. Riprova più tardi.', {
          position: 'bottom-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      }
    } finally {
      setIsSendingForPreApproval(false);
    }
  };

  const handleAcknowledgeContribution = async () => {
    // Controllo aggiuntivo lato client
    if (!hasAction(UserAction.CLOSE_USER_CONTRIBUTION)) {
      toast.error('Non hai i permessi necessari per confermare la contribuzione.', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    try {
      setIsAcknowledgingContribution(true);
      await FlowUserService.acknowledgeContribution(document.id!);
      
      toast.success('Contribuzione confermata con successo!', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      
      // Aggiorna lo stato locale per nascondere il bottone
      setCurrentUserAcknowledged(true);
      
      if (onRefresh) {
        onRefresh();
      }
    } catch (error: any) {
      console.error('Errore durante la conferma della contribuzione:', error);
      
      if (error?.response?.status === 403) {
        toast.error('Non hai i permessi necessari per confermare la contribuzione.', {
          position: 'bottom-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      } else {
        toast.error('Si è verificato un errore durante la conferma della contribuzione. Riprova più tardi.', {
          position: 'bottom-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      }
    } finally {
      setIsAcknowledgingContribution(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerTop}>
          <div className={styles.headerMainInfo}>
            <div className={styles.headerField}>
              <label className={styles.headerLabel}>
                <HiOutlineLightBulb className={styles.icon} /> Oggetto
              </label>
              <h1 className={styles.headerValue}>{document.subject || 'Non specificato'}</h1>
            </div>
            
            <div className={styles.headerField}>
              <label className={styles.headerLabel}>
                <HiOutlineUsers className={styles.icon} /> {document.flowType?.name || 'Documento'} per il
              </label>
              <h2 className={styles.headerValue}>{'Non specificato'}</h2>
            </div>
          </div>

          <div className={styles.headerRightInfo}>
            <div className={styles.documentType}>
              {document.flowType?.name || 'Tipo non specificato'}
            </div>
            <div style={getFlowStatusStyles(document.flowStatus || FlowStatus._0)}>
              {getFlowStatusLabel(document.flowStatus || FlowStatus._0)}
            </div>
            <div className={styles.documentDate}>
              <HiOutlineClock className={styles.icon} /> {formatDate(document.creationTime)}
            </div>
          </div>
        </div>

        <div className={styles.headerBottom}>
          <div className={styles.leftSection}>
            <div className={styles.attributesSection}>
              <div className={styles.attributesList}>
                {attributesWithNames.map((attr, index) => (
                  <div key={index} className={styles.attributeItem}>
                    <div className={styles.attributeLabel}>
                      <HiOutlineTag className={styles.icon} /> {attr.name}
                    </div>
                    <div className={styles.attributeValue}>
                      {attr.value}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className={styles.departmentInfo}>
              <div className={styles.departmentBadge}>
                <HiOutlineOfficeBuilding className={styles.icon} />
                <span className={styles.departmentCode}>{creatorInfo.rank} {creatorInfo.name}</span>
                <span className={styles.departmentName}>{creatorInfo.organizationUnit}</span>
              </div>
            </div>
          </div>

          <div className={styles.headerActions}>
            {hasAction(UserAction.SELECT_CONTRIBUTORS) && (
              <Button 
                type="primary" 
                onClick={() => setShowContributorsModal(true)}
                className={styles.contributorsButton}
              >
                <div className={styles.buttonContent}>
                  <HiOutlineUsers className={styles.buttonIcon} />
                  <span>Contributori</span>
                </div>
              </Button>
            )}
            {hasAction(UserAction.SELECT_COORDINATION_UNIT) && (
              <Button 
                type="primary" 
                onClick={() => setShowCoordinationModal(true)}
                className={styles.coordinateButton}
              >
                <div className={styles.buttonContent}>
                  <HiOutlineUsers className={styles.buttonIcon} />
                  <span>Coordinamenti</span>
                </div>
              </Button>
            )}
            <Button 
              type="secondary" 
              onClick={onHistory}
              className={styles.historyButton}
            >
              <div className={styles.buttonContent}>
                <HiOutlineClipboardList className={styles.buttonIcon} />
                <span>Storico</span>
              </div>
            </Button>
          </div>
        </div>
      </div>

      <div className={styles.sheetsContainer}>
        {loading ? (
          <div className={styles.loading}>Caricamento...</div>
        ) : flowTypeSheets.length === 0 ? (
          <div className={styles.noSheets}>Nessun sheet configurato per questo tipo di flusso</div>
        ) : (
          flowTypeSheets.map(flowTypeSheet => {
            // Trova lo Sheet reale corrispondente a questo FlowTypeSheet
            const realSheet = flowSheets.find(fs => fs.flowTypeSheetId === flowTypeSheet.id);
            
            return (
              <div key={flowTypeSheet.id} className={styles.sheetSection}>
                <div className={styles.sheetHeader}>
                  {getSheetIcon()}
                  <div className={styles.sheetTitleContainer}>
                    <h3 className={styles.sheetTitle}>{flowTypeSheet.name}</h3>
                    {flowTypeSheet.description && (
                      <Tooltip content={flowTypeSheet.description} title="">
                        <div className={appStyles.infoIcon}>i</div>
                      </Tooltip>
                    )}
                  </div>
                  {/* {realSheet ? (
                    <span className={styles.sheetId}>ID: {realSheet.id}</span>
                  ) : (
                    <span className={styles.sheetWarning}>Sheet non creato</span>
                  )} */}
                </div>
                
                <div className={styles.sheetContent}>
                  <div className={styles.textEditorContainer}>
                    {realSheet ? (
                      /* TextEditor con backend integrato - usa l'ID dello Sheet reale */
                      <TextEditor 
                        sheetId={realSheet.id}
                        flowStatus={document.flowStatus || FlowStatus._0}
                        initialContent={realSheet.content && typeof realSheet.content === 'string' ? realSheet.content : undefined}
                        canEditContent={canEditContent}
                        canComment={canComment}
                      />
                    ) : (
                      <div className={styles.sheetNotAvailable}>
                        Sheet non disponibile. Il sheet deve essere creato automaticamente quando viene creato il flusso.
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      <div className={styles.attachmentsSection}>
        <div className={styles.sectionHeader}>
          <h3 className={styles.sectionTitle}>Allegati</h3>
        </div>
        <div className={styles.attachmentsContent}>
          {hasAction(UserAction.MANAGE_ATTACHMENTS) && (
            <Button type="primary" className={styles.attachButton}>
              <HiOutlinePaperClip className={styles.buttonIcon} /> Aggiungi Allegato
            </Button>
          )}
          <Button type="secondary" className={styles.pdfButton}>
            <HiOutlineDocumentDuplicate className={styles.buttonIcon} /> Stampa in PDF
          </Button>
        </div>
      </div>

      <div className={styles.bottomActions}>
        {hasAction(UserAction.PUBLISH_DRAFT) && (
          <Button 
            type="secondary" 
            className={styles.publishButton} 
            buttonType="button"
            onClick={() => setShowPublishConfirm(true)}
            disabled={isPublishing}
          >
            {isPublishing ? (
              <span>Pubblicazione in corso...</span>
            ) : (
              <>
                <span className={styles.publishIcon}>📄</span> Pubblica Bozza
              </>
            )}
          </Button>
        )}
        {hasAction(UserAction.SEND_FOR_PRE_APPROVAL) && (
          <Button 
            type="primary" 
            className={styles.sendForPreApprovalButton} 
            buttonType="button"
            onClick={() => setShowSendForPreApprovalConfirm(true)}
            disabled={isSendingForPreApproval}
          >
            {isSendingForPreApproval ? (
              <span>Invio in corso...</span>
            ) : (
              <>
                <span className={styles.preApprovalIcon}>✓</span> Invia in Pre-Approvazione
              </>
            )}
          </Button>
        )}
        {hasAction(UserAction.APPROVE) && (
          <Button type="secondary" className={styles.approvalButton} buttonType="button">
            <span className={styles.approvalIcon}>✓</span> Approvo
          </Button>
        )}
        {hasAction(UserAction.REQUEST_TO_SPEAK) && (
          <Button 
            type="secondary" 
            className={styles.talkButton} 
            buttonType="button"
            onClick={() => setShowSpeakModal(true)}
          >
            <span className={styles.talkIcon}>💬</span> Prego parlare
          </Button>
        )}
        {hasAction(UserAction.FORWARD_TO_SUPERVISOR) && (
          <Button type="primary" className={styles.forwardButton} buttonType="button">
            <span className={styles.forwardIcon}>➜</span> Inoltra
          </Button>
        )}
        {hasAction(UserAction.DISAPPROVE) && (
          <Button type="secondary" className={styles.rejectButton} buttonType="button">
            <span className={styles.rejectIcon}>✕</span> Non approvo
          </Button>
        )}
        {hasAction(UserAction.CLOSE_USER_CONTRIBUTION) && currentUserAcknowledged !== true && (
          <Button 
            type="secondary" 
            className={styles.acknowledgeContributionButton} 
            buttonType="button"
            onClick={() => setShowAcknowledgeContributionConfirm(true)}
            disabled={isAcknowledgingContribution}
          >
            {isAcknowledgingContribution ? (
              <span>Conferma in corso...</span>
            ) : (
              <>
                <span className={styles.acknowledgeIcon}>✅</span> Conferma Fine Contribuzione
              </>
            )}
          </Button>
        )}
      </div>

      {/* Modali */}
      <ContributorsModal 
        isOpen={showContributorsModal}
        onClose={() => setShowContributorsModal(false)}
        flowId={document.id || ''}
        onSave={onRefresh}
      />

      <CoordinationModal 
        isOpen={showCoordinationModal}
        onClose={() => setShowCoordinationModal(false)}
        onViewDetail={handleViewDetail}
        flowId={document.id || ''}
      />

      <SpeakModal 
        isOpen={showSpeakModal}
        onClose={() => setShowSpeakModal(false)}
        onSubmit={handleSpeakSubmit}
      />

      <ConfirmDialog
        isOpen={showPublishConfirm}
        onClose={() => setShowPublishConfirm(false)}
        onConfirm={handlePublishDraft}
        title="Pubblica Bozza"
        message="Sei sicuro di voler pubblicare questa bozza?"
        confirmLabel="Pubblica"
        cancelLabel="Annulla"
        confirmType="primary"
      />

      <ConfirmDialog
        isOpen={showSendForPreApprovalConfirm}
        onClose={() => setShowSendForPreApprovalConfirm(false)}
        onConfirm={handleSendForPreApproval}
        title="Invia in Pre-Approvazione"
        message="Sei sicuro di voler inviare questo documento in pre-approvazione?"
        confirmLabel="Invia"
        cancelLabel="Annulla"
        confirmType="primary"
      />

      <ConfirmDialog
        isOpen={showAcknowledgeContributionConfirm}
        onClose={() => setShowAcknowledgeContributionConfirm(false)}
        onConfirm={handleAcknowledgeContribution}
        title="Conferma Fine Contribuzione"
        message="Sei sicuro di voler confermare la fine della tua contribuzione a questo documento?"
        confirmLabel="Conferma"
        cancelLabel="Annulla"
        confirmType="primary"
      />
    </div>
  );
};

export default FlowDetails; 