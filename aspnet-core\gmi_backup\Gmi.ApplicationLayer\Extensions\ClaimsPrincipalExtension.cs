﻿using Gmi.DomainModelLayer.ValueObjects;
using System.Security.Claims;

namespace Gmi.ApplicationLayer.Extensions
{
    public static class ClaimsPrincipalExtension
    {
        public static IEnumerable<RoleType> GetRoleTypesFromHttpContext(this ClaimsPrincipal claimsPrincipal)
        {
            return claimsPrincipal.GetAllClaims(ClaimTypes.Role, c => RoleType.GetByValue(c.Value));

        }

        public static IEnumerable<T> GetAllClaims<T>(this ClaimsPrincipal claimsPrincipal, string claimKey, Func<Claim, T>? converter = null)
        {
            converter ??= (c) => (T)Convert.ChangeType(c.Value, typeof(T));
            return claimsPrincipal.FindAll(claimKey)
                ?.Select(converter)
                ?.ToList() ?? [];
        }

        public static T GetSingleClaim<T>(this ClaimsPrincipal claimsPrincipal, string claimKey, Func<Claim, T>? converter = null)
        {
            converter ??= (c) => (T)Convert.ChangeType(c.Value, typeof(T));
            var claim = claimsPrincipal.FindFirst(claimKey);
            return claim == null ? default : converter(claim);
        }

    }
}
