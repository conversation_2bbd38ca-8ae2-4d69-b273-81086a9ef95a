import { SheetCommentCreateDto, SheetCommentDto, SheetCommentUpdateDto } from '../models';
import { FlowStatus } from '../enums';
import { SheetCommentClient } from '../proxy';
import { httpClient } from '../httpClient';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7079';

/**
 * Service wrapper per SheetCommentClient
 * Fornisce metodi statici per interagire con le API
 * Generato automaticamente - Non modificare manualmente
 */
export class SheetCommentService {
  private static sheetCommentClient = new SheetCommentClient(
    API_BASE_URL,
    httpClient()
  );

  /**
   * getBySheetId - Ottiene tutti i commenti di un foglio con filtro di visibilità
   * @param sheetId ID del foglio
   * @param currentFlowStatus Stato corrente del flusso per filtrare i commenti visibili
   * @param signal AbortSignal opzionale
   * @returns Promise che risolve in SheetCommentDto[]
   */
  static getBySheetId(sheetId: string, currentFlowStatus?: FlowStatus, signal?: AbortSignal): Promise<SheetCommentDto[]> {
    const url = `${API_BASE_URL}/api/SheetComment/sheet/${sheetId}${currentFlowStatus !== undefined ? `?currentFlowStatus=${currentFlowStatus}` : ''}`;
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      },
      signal
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    });
  }

  /**
   * getBySheetIdAndRefId - Ottiene tutti i commenti di un foglio con un RefId specifico con filtro di visibilità
   * @param sheetId ID del foglio
   * @param refId Riferimento HTML dell'elemento
   * @param currentFlowStatus Stato corrente del flusso per filtrare i commenti visibili
   * @param signal AbortSignal opzionale
   * @returns Promise che risolve in SheetCommentDto[]
   */
  static getBySheetIdAndRefId(sheetId: string, refId: string, currentFlowStatus?: FlowStatus, signal?: AbortSignal): Promise<SheetCommentDto[]> {
    const url = `${API_BASE_URL}/api/SheetComment/sheet/${sheetId}/ref/${refId}${currentFlowStatus !== undefined ? `?currentFlowStatus=${currentFlowStatus}` : ''}`;
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      },
      signal
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    });
  }

  /**
   * getMainCommentsWithReplies - Ottiene i commenti principali con le loro risposte con filtro di visibilità
   * @param sheetId ID del foglio
   * @param currentFlowStatus Stato corrente del flusso per filtrare i commenti visibili
   * @param signal AbortSignal opzionale
   * @returns Promise che risolve in SheetCommentDto[]
   */
  static getMainCommentsWithReplies(sheetId: string, currentFlowStatus?: FlowStatus, signal?: AbortSignal): Promise<SheetCommentDto[]> {
    const url = `${API_BASE_URL}/api/SheetComment/sheet/${sheetId}/main${currentFlowStatus !== undefined ? `?currentFlowStatus=${currentFlowStatus}` : ''}`;
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      },
      signal
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    });
  }

  /**
   * getById - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto
   */
  static getById(id: string, signal?: AbortSignal): Promise<SheetCommentDto> {
    return this.sheetCommentClient.getById(id, signal);
  }

  /**
   * update - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static update(id: string, body: SheetCommentUpdateDto | undefined, signal?: AbortSignal): Promise<void> {
    return this.sheetCommentClient.update(id, body, signal);
  }

  /**
   * delete - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static delete(id: string, signal?: AbortSignal): Promise<void> {
    return this.sheetCommentClient.delete(id, signal);
  }

  /**
   * getWithReplies - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto
   */
  static getWithReplies(commentId: string, signal?: AbortSignal): Promise<SheetCommentDto> {
    return this.sheetCommentClient.getWithReplies(commentId, signal);
  }

  /**
   * create - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto
   */
  static create(body: SheetCommentCreateDto | undefined, signal?: AbortSignal): Promise<SheetCommentDto> {
    return this.sheetCommentClient.create(body, signal);
  }

  /**
   * hasReplies - Generato automaticamente
   * @returns Promise che risolve in boolean
   */
  static hasReplies(commentId: string, signal?: AbortSignal): Promise<boolean> {
    return this.sheetCommentClient.hasReplies(commentId, signal);
  }
}
