using System;
using System.Collections.Generic;
using System.Linq;
using S2M.DomainModelLayer.Enums;

namespace S2M.DomainModelLayer.Constants
{
    /// <summary>
    /// Matrice di visibilità dei commenti basata sullo stato del flusso al momento della creazione del commento
    /// e sullo stato corrente del flusso
    /// </summary>
    public static class CommentVisibilityMatrix
    {
        /// <summary>
        /// Mappa che definisce per ogni stato del flusso corrente, 
        /// quali commenti (basati sul loro stato di creazione) sono visibili
        /// Key: FlowStatus corrente
        /// Value: Array di FlowStatus dei commenti che sono visibili
        /// </summary>
        public static readonly Dictionary<FlowStatus, FlowStatus[]> VisibleCommentsByCurrentStatus = 
            new Dictionary<FlowStatus, FlowStatus[]>
            {
                // BOZZA (Draft = 0)
                // In bozza si vedono solo i commenti creati in bozza
                [FlowStatus.Draft] = new[] { FlowStatus.Draft },

                // STESURA (Writing = 1)  
                // In stesura si vedono:
                // - commenti aggiunti in fase bozza (solo in bozza e stesura)
                // - commenti aggiunti in stesura (solo in fase stesura)
                // - commenti aggiunti in coordinamento (in coordinamento e in stesura)
                [FlowStatus.Writing] = new[] { FlowStatus.Draft, FlowStatus.Writing, FlowStatus.Coordination },

                // PRE-APPROVAZIONE (PreApproval = 2)
                // In pre-approvazione si vedono:
                // - commenti aggiunti in pre-approvazione (in pre-approvazione e pre-approvato)
                [FlowStatus.PreApproval] = new[] { FlowStatus.PreApproval },

                // PRE-APPROVATO (PreApproved = 3)
                // In pre-approvato si vedono:
                // - commenti aggiunti in pre-approvazione (in pre-approvazione e pre-approvato)
                // - commenti aggiunti in pre-approvato (solo in pre-approvato)
                [FlowStatus.PreApproved] = new[] { FlowStatus.PreApproval, FlowStatus.PreApproved },

                // COORDINAMENTO (Coordination = 4)
                // In coordinamento si vedono:
                // - commenti aggiunti in coordinamento (in coordinamento e in stesura)
                [FlowStatus.Coordination] = new[] { FlowStatus.Coordination },

                // REVISIONE (Revision = 5)
                // In revisione si vedono tutti i commenti precedenti
                [FlowStatus.Revision] = new[] { 
                    FlowStatus.Draft, FlowStatus.Writing, FlowStatus.PreApproval, 
                    FlowStatus.PreApproved, FlowStatus.Coordination, FlowStatus.Revision 
                },

                // APPROVAZIONE (Approval = 6)
                // In approvazione si vedono tutti i commenti precedenti
                [FlowStatus.Approval] = new[] { 
                    FlowStatus.Draft, FlowStatus.Writing, FlowStatus.PreApproval, 
                    FlowStatus.PreApproved, FlowStatus.Coordination, FlowStatus.Revision, FlowStatus.Approval 
                },

                // PUBBLICATO (Published = 7)
                // Quando pubblicato si vedono tutti i commenti
                [FlowStatus.Published] = new[] { 
                    FlowStatus.Draft, FlowStatus.Writing, FlowStatus.PreApproval, 
                    FlowStatus.PreApproved, FlowStatus.Coordination, FlowStatus.Revision, 
                    FlowStatus.Approval, FlowStatus.Published 
                },

                // RIFIUTATO (Rejected = 8)
                // Quando rifiutato si vedono tutti i commenti
                [FlowStatus.Rejected] = new[] { 
                    FlowStatus.Draft, FlowStatus.Writing, FlowStatus.PreApproval, 
                    FlowStatus.PreApproved, FlowStatus.Coordination, FlowStatus.Revision, 
                    FlowStatus.Approval, FlowStatus.Rejected 
                },

                // ANNULLATO (Canceled = 9)
                // Quando annullato si vedono tutti i commenti
                [FlowStatus.Canceled] = new[] { 
                    FlowStatus.Draft, FlowStatus.Writing, FlowStatus.PreApproval, 
                    FlowStatus.PreApproved, FlowStatus.Coordination, FlowStatus.Revision, 
                    FlowStatus.Approval, FlowStatus.Canceled 
                }
            };

        /// <summary>
        /// Verifica se un commento è visibile in base al suo stato di creazione e allo stato corrente del flusso
        /// </summary>
        /// <param name="currentFlowStatus">Stato corrente del flusso</param>
        /// <param name="commentFlowStatus">Stato del flusso al momento della creazione del commento</param>
        /// <returns>True se il commento è visibile, false altrimenti</returns>
        public static bool IsCommentVisible(FlowStatus currentFlowStatus, FlowStatus commentFlowStatus)
        {
            if (!VisibleCommentsByCurrentStatus.ContainsKey(currentFlowStatus))
                return false;

            var visibleCommentStatuses = VisibleCommentsByCurrentStatus[currentFlowStatus];
            return Array.Exists(visibleCommentStatuses, status => status == commentFlowStatus);
        }

        /// <summary>
        /// Ottiene tutti gli stati dei commenti visibili per un determinato stato corrente del flusso
        /// </summary>
        /// <param name="currentFlowStatus">Stato corrente del flusso</param>
        /// <returns>Array degli stati dei commenti visibili</returns>
        public static FlowStatus[] GetVisibleCommentStatuses(FlowStatus currentFlowStatus)
        {
            if (!VisibleCommentsByCurrentStatus.ContainsKey(currentFlowStatus))
                return new FlowStatus[0];

            return VisibleCommentsByCurrentStatus[currentFlowStatus];
        }
    }
}
