﻿using Gmi.DomainModelLayer.Enums;
using Microsoft.AspNetCore.Authorization;

namespace Gmi.ApplicationLayer.Helpers.Security
{
    public class FeatureAuthorizeAttribute : AuthorizeAttribute
    {
        const string POLICY_PREFIX = "Feature";

        public FeatureAuthorizeAttribute(params Features[] features) => Features = features;

        public FeatureAuthorizeAttribute() => Features = [];

        public Features[] Features
        {
            get
            {
                var featuresString = Policy?[POLICY_PREFIX.Length..];
                if (!string.IsNullOrEmpty(featuresString))
                {
                    return featuresString.Split('|').Select(Enum.Parse<Features>).ToArray();
                }

                return [];
            }
            set
            {
                Policy = $"{POLICY_PREFIX}{string.Join('|', value.Select(v => v.ToString()))}";
            }
        }


    }

}
