﻿using Gmi.ApplicationLayer.Services.Interfaces;
using Microsoft.Extensions.Options;

namespace Gmi.ApplicationLayer.Helpers
{
    public class FileServiceFactory(
        IServiceProvider serviceProvider, 
        IOptions<Settings> settings,
        IEnumerable<Lazy<IFileService>> fileServices) : IFileServiceFactory
    {
        private readonly Settings _settings = settings.Value;
        private readonly IEnumerable<Lazy<IFileService>> _fileServices = fileServices;

        public IFileService Create()
        {
            var providerName = _settings.FileService.UsedProviderName;
            var provider = _fileServices
                .Select(p => p.Value)
                .FirstOrDefault(p => p.GetType().Name.Equals(providerName));

            if (provider != null)
            {
                return provider;
            }

            throw new ArgumentException($"Unsupported file service name: {providerName}");
        }
    }
}
