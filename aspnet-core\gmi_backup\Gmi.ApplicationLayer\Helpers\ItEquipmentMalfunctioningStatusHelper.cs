﻿using Gmi.DomainModelLayer.Enums;
using Gmi.DomainModelLayer.ValueObjects;

namespace Gmi.ApplicationLayer.Helpers
{
    public static class ItEquipmentMalfunctioningStatusHelper
    {
        private static readonly Dictionary<RoleType, HashSet<(ItEquipmentStatuses CurrentStatus, ItEquipmentStatuses DestinationStatus)>> transitionRules = new()
        {
            {
                RoleType.ReferenteInformatico, new HashSet<(ItEquipmentStatuses, ItEquipmentStatuses)>
                {
                    (ItEquipmentStatuses.ReportedMalfunction, ItEquipmentStatuses.Installed),
                    (ItEquipmentStatuses.ReportedMalfunction, ItEquipmentStatuses.UnderRepair),
                    (ItEquipmentStatuses.ReportedMalfunction, ItEquipmentStatuses.UnderRepair_ExternallyDelivered),
                    (ItEquipmentStatuses.UnderRepair_ExternallyDelivered, ItEquipmentStatuses.Installed),
                    (ItEquipmentStatuses.UnderRepair, ItEquipmentStatuses.UnderRepair_OT),
                    (ItEquipmentStatuses.UnderRepair_PickedUp, ItEquipmentStatuses.Installed)
                }
            },
            {
                RoleType.OrganoTecnico, new HashSet<(ItEquipmentStatuses, ItEquipmentStatuses)>
                {
                    (ItEquipmentStatuses.UnderRepair_OT, ItEquipmentStatuses.UnderRepair_OT_ExternallyDelivered),
                    (ItEquipmentStatuses.UnderRepair_OT, ItEquipmentStatuses.UnderRepair_OT_AvailableForPickup),
                    (ItEquipmentStatuses.UnderRepair_OT_AvailableForPickup, ItEquipmentStatuses.UnderRepair_PickedUp),
                    (ItEquipmentStatuses.UnderRepair_OT_ExternallyDelivered, ItEquipmentStatuses.UnderRepair_OT_AvailableForPickup),
                    (ItEquipmentStatuses.UnderRepair_OT, ItEquipmentStatuses.ReportedOutOfOrder)
                }
            },
            {
                RoleType.OrganoTecnicoSupervisore, new HashSet<(ItEquipmentStatuses, ItEquipmentStatuses)>
                {
                    (ItEquipmentStatuses.ReportedOutOfOrder, ItEquipmentStatuses.OutOfOrder)
                }
            },
        };

        private static readonly Dictionary<RoleType, HashSet<ItEquipmentStatuses>> viewableStates = new()
        {
            {
                RoleType.ReferenteInformatico, new HashSet<ItEquipmentStatuses>
                {
                    ItEquipmentStatuses.ReportedMalfunction,
                    ItEquipmentStatuses.UnderRepair_ExternallyDelivered,
                    ItEquipmentStatuses.UnderRepair,
                    ItEquipmentStatuses.UnderRepair_PickedUp,
                }
            },
            {
                RoleType.OrganoTecnico, new HashSet<ItEquipmentStatuses>
                {
                    ItEquipmentStatuses.UnderRepair_OT,
                    ItEquipmentStatuses.UnderRepair_OT_AvailableForPickup,
                    ItEquipmentStatuses.UnderRepair_OT_ExternallyDelivered,
                }
            },
            {
                RoleType.OrganoTecnicoSupervisore, new HashSet<ItEquipmentStatuses>
                {
                    ItEquipmentStatuses.ReportedOutOfOrder,
                }
            },
            {
                RoleType.AmministratoreGenerale, new HashSet<ItEquipmentStatuses>
                {
                    ItEquipmentStatuses.ReportedMalfunction,
                    ItEquipmentStatuses.UnderRepair_ExternallyDelivered,
                    ItEquipmentStatuses.UnderRepair,
                    ItEquipmentStatuses.UnderRepair_OT,
                    ItEquipmentStatuses.UnderRepair_OT_ExternallyDelivered,
                    ItEquipmentStatuses.UnderRepair_OT_AvailableForPickup,
                    ItEquipmentStatuses.UnderRepair_PickedUp,
                    ItEquipmentStatuses.ReportedOutOfOrder,
                }

            }

        };

        public static bool TransitionAllowed(ItEquipmentStatuses currentStatus, ItEquipmentStatuses destinationStatus, RoleType role)
        {
            return transitionRules.TryGetValue(role, out var allowedTransitions) && allowedTransitions.Contains((currentStatus, destinationStatus));
        }

        public static HashSet<ItEquipmentStatuses> GetViewableStatuses(RoleType role)
        {
            return viewableStates.TryGetValue(role, out var states) ? states : [];
        }
    }
}
