﻿using Gmi.DomainModelLayer.ValueObjects;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;

namespace Gmi.ApplicationLayer.Extensions
{
    public static class HttpContextExtension
    {
        public static IEnumerable<RoleType> GetRoleTypesFromHttpContext(this HttpContext httpContext)
        {
            return httpContext.User.GetRoleTypesFromHttpContext();
        }

        public static IEnumerable<T> GetAllClaims<T>(this HttpContext httpContext, string claimKey, Func<Claim, T>? converter = null)
        {
            return httpContext.User.GetAllClaims(claimKey, converter);
        }

        public static T GetSingleClaim<T>(this HttpContext httpContext, string claimKey, Func<Claim, T>? converter = null)
        {
            return httpContext.User.GetSingleClaim(claimKey, converter);
        }

    }
}
