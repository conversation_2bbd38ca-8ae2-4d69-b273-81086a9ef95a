﻿using Gmi.ApplicationLayer.Handlers;
using Gmi.ApplicationLayer.Helpers;
using Gmi.ApplicationLayer.Services.Implementations;
using Gmi.ApplicationLayer.Services.Implementations.Simpers;
using Gmi.ApplicationLayer.Services.Interfaces;
using Gmi.ApplicationLayer.Services.Interfaces.Simpers;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System.Reflection;

namespace Gmi.ApplicationLayer.Extensions
{
    public static class ServiceBuilderExtensions
    {
        public static void AddEntityServices(this IServiceCollection services)
        {
            services.AddScoped<IUsersService, UsersService>();
            services.AddTransient<IMailService, MailService>();
            services.AddScoped<IAreasService, AreasService>();
            services.AddScoped<IInstitutionsService, InstitutionsService>();
            services.AddScoped<IItemService, ItemService>();
            services.AddScoped<IItemTypesService, ItemTypesService>();
            services.AddScoped<ICategoriesService, CategoriesService>();
            services.AddScoped<ICapacitySizesService, CapacitySizesService>();
            services.AddScoped<IPortsService, PortsService>();
            services.AddScoped<IConnectionTypesService, ConnectionTypesService>();
            services.AddScoped<IScreenSizesService, ScreenSizesService>();
            services.AddScoped<IModelsService, ModelsService>();
            services.AddScoped<IBrandsService, BrandsService>();
            services.AddScoped<IMaterialsService, MaterialsService>();
            services.AddScoped<IFormatsService, FormatsService>();
            services.AddScoped<IProcessorsService, ProcessorsService>();
            services.AddScoped<IWarehousesService, WarehousesService>();
            services.AddScoped<IFrequenciesService, FrequenciesService>();
            services.AddScoped<IMaterialForbiddenPropertiesService, MaterialForbiddenPropertiesService>();
            services.AddScoped<IItEquipmentsService, ItEquipmentsService>();
            services.AddScoped<IInstitutionCensusesService, InstitutionCensusesService>();
            services.AddScoped<IWarehouseCensusesService, WarehouseCensusesService>();
            services.AddScoped<INotificationsService, NotificationsService>();
            services.AddScoped<IFilesEntityService, FilesEntityService>();
            services.AddScoped<IFeaturesService, FeaturesService>();
            services.AddScoped<IRoleFeaturesService, RoleFeaturesService>();
            services.AddScoped<IRoleRequestsService, RoleRequestsService>();
            services.AddScoped<INotificationTemplateService, NotificationTemplateService>();
            services.AddScoped<IOrdersService, OrdersService>();
            services.AddScoped<IDocumentTemplateService, DocumentTemplateService>();
            services.AddScoped<IMovementOrdersService, MovementOrdersService>();

            //Simpers Services
            services.AddScoped<IEmployeeService, EmployeeService>();

        }

        public static void AddUtilityServices(this WebApplicationBuilder builder)
        {
            builder.Services.AddHttpContextAccessor();
            builder.Services.AddScoped<INotificationProviderFactory, NotificationProviderFactory>();
            builder.Services.AddScoped<IFileServiceFactory, FileServiceFactory>();
            builder.Services.AddScoped<ILoggedUserService, LoggedUserService>();
            builder.Services.AddSignalR();
            builder.Services.AddHttpClient<IUsersService, UsersService>(client =>
            {
                client.DefaultRequestHeaders.Add("Accept", "application/json");
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ClientCertificateOptions = ClientCertificateOption.Manual,
                ServerCertificateCustomValidationCallback = delegate { return true; }
            })
            .ConfigureAdditionalHttpMessageHandlers((delegates, services) =>
            {
                delegates.Add(services.GetRequiredService<ProtectedApiBearerTokenHandler>());
            });

            builder.Services.AddHttpClient<IConverterService, ConverterService>().ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ClientCertificateOptions = ClientCertificateOption.Manual,
                ServerCertificateCustomValidationCallback = delegate { return true; },

            }).ConfigureAdditionalHttpMessageHandlers((delegates, services) =>
            {
                delegates.Add(services.GetRequiredService<PdfConverterApiKeyHandler>());
            });
        }

        public static void AddAllProvidersLazilyScoped<T>(this IServiceCollection services, Assembly assembly = null)
        {
            var types = (assembly ?? Assembly.GetExecutingAssembly())
                .GetTypes()
                .Where(p => typeof(T).IsAssignableFrom(p) && !p.IsInterface && !p.IsAbstract);

            foreach (var type in types)
            {
                services.AddScoped(typeof(Lazy<T>), serviceProvider =>
                {
                    return new Lazy<T>(() => (T)serviceProvider.GetRequiredService(type));
                });
                services.AddScoped(type);
            }
        }

    }

}
