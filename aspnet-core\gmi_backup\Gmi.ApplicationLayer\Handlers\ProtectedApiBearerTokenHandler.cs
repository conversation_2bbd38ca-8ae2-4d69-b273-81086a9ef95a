﻿using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using Gmi.ApplicationLayer.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace Gmi.ApplicationLayer.Handlers
{
    public class ProtectedApiBearerTokenHandler : DelegatingHandler
    {
        private readonly ITokenService _identityServerClient;

        public ProtectedApiBearerTokenHandler(
            ITokenService identityServerClient)
        {
            _identityServerClient = identityServerClient
                ?? throw new ArgumentNullException(nameof(identityServerClient));
        }

        protected override async Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request,
            CancellationToken cancellationToken)
        {
            var accessToken = await _identityServerClient.GetTokenByClientSecret();
            request.Headers.Add("Authorization", "Bearer " + accessToken.access_token);
            return await base.SendAsync(request, cancellationToken);
        }
    }

    public class PdfConverterApiKeyHandler(IConfiguration config) : DelegatingHandler
    {
        protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
            CancellationToken cancellationToken)
        {
            request.Headers.Add(config["Settings:Converter:ProjectName"], config["Settings:Converter:ProjectKey"]);
            return await base.SendAsync(request, cancellationToken);
        }
    }
}
