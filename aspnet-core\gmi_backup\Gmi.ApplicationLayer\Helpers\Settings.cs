﻿using Gmi.ApplicationLayer.Services.Interfaces;
using System.Text.Json.Serialization;

namespace Gmi.ApplicationLayer.Helpers
{
    public class Settings
    {
        public ConnectionStrings? ConnectionStrings { get; set; }
        public Logging? Logging { get; set; }
        public string[]? AllowedOrigins { get; set; }
        public string? AllowedHosts { get; set; }
        public JwtSettings? Jwt { get; set; }
        public Claims? Claims { get; set; }
        public UserAttributes? UserAttributes { get; set; }
        public MailSettings? MailSettings { get; set; }
        public SignalRSettings? SignalR { get; set; }
        public ConverterSettings? Converter { get; set; }
        public FileServiceSettings FileService { get; set; }
    }

    public class ConnectionStrings
    {
        public required string SqlConnectionString { get; set; }
    }

    public class Logging
    {
        public required LogLevel LogLevel { get; set; }
    }


    public class LogLevel
    {
        [JsonPropertyName("Default")]
        public required string Default { get; set; }
        [JsonPropertyName("Microsoft.AspNetCore")]
        public required string MicrosoftAspNetCore { get; set; }

        [JsonPropertyName("System.Net.Http.HttpClient")]
        public required string SystemNetHttpHttpClient { get; set; }

        [JsonPropertyName("Microsoft.EntityFrameworkCore.Database.Command")]
        public required string MicrosoftEntityFrameworkCoreDatabaseCommand { get; set; }
    }



    public class JwtSettings
    {
        public required string Issuer { get; set; }
        public required string Audience { get; set; }
        public required string AudienceFE { get; set; }
        public required string Realm { get; set; }
        public required string Key { get; set; }
        public required string Secret { get; set; }
    }

    public class Claims
    {
        public required string Username { get; set; }
        public required string FullName { get; set; }
        public required string ItRepresentativeInstitutions { get; set; }
        public required string WarehouseManagerWarehouses { get; set; }
        public required string TechnicalBodyWarehouses { get; set; }
        public required string SupervisorTechnicalBodyWarehouses { get; set; }
        public required string CategoryManagerCategoryAreas { get; set; }
        public required string Rank { get; set; }
        public required string InstitutionId { get; set; }
    }

    public class UserAttributes
    {
        public required string ItRepresentativeInstitutions { get; set; }
        public required string WarehouseManagerWarehouses { get; set; }
        public required string TechnicalBodyWarehouses { get; set; }
        public required string SupervisorTechnicalBodyWarehouses { get; set; }
    }

    public class MailSettings
    {
        public required string Server { get; set; }
        public int Port { get; set; }
        public required string SenderName { get; set; }
        public required string SenderEmail { get; set; }
        public required string UserName { get; set; }
        public required string Password { get; set; }
    }

    public class SignalRSettings
    {
        public required string NotificationMethod { get; set; }
    }

    public class ConverterSettings
    {
        public required string Url { get; set; }
        public required string ProjectName { get; set; }
        public required string ProjectKey { get; set; }
    }

    public class FileServiceSettings
    {
        public string UsedProviderName { get; set; }
        public LocalFileServiceSettings LocalProvider{ get; set; }
        public AzureFileServiceSettings AzureProvider{ get; set; }
        public MinioFileServiceSettings MinioProvider{ get; set; }
    }

    public class LocalFileServiceSettings
    {
        public string DirectoryPath { get; set; }
    }

    public class AzureFileServiceSettings
    {
        public string Container { get; set; }
        public string ConnectionString { get; set; }
    }

    public class MinioFileServiceSettings
    {
        public string Bucket { get; set; }
        public string ServiceUrl { get; set; }
        public string AccessKey { get; set; }
        public string SecretKey { get; set; }
        public string Region { get; set; }
    }
}





