﻿using Gmi.ApplicationLayer.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;

namespace Gmi.ApplicationLayer.Helpers.Security
{
    public class FeatureAuthorizationHandler(ILoggedUserService _loggedUserService) : AuthorizationHandler<FeatureRequirement>
    {
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, FeatureRequirement requirement)
        {
            try
            {
                var features = requirement.Features;
                var userFeatures = await _loggedUserService.GetCurrentUserEnabledFeatures();
                if (userFeatures != null && userFeatures.Any(uf => features.Any(f => f == uf.FeatureId)))
                {
                    context.Succeed(requirement);
                }
                else
                {
                    context.Fail(new AuthorizationFailureReason(this, "User is not allowed to access any of the required features"));
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }
    }
}
