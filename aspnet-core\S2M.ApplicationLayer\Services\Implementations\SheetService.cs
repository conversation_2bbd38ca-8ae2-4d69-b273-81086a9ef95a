using AutoMapper;
using Microsoft.Extensions.Logging;
using S2M.ApplicationLayer.Dto;
using S2M.ApplicationLayer.Services.Interfaces;
using S2M.DomainModelLayer.Entities;
using S2M.InfrastructureLayer.Repository;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Text.RegularExpressions;

namespace S2M.ApplicationLayer.Services.Implementations
{
    public class SheetService : ISheetService
    {
        private readonly IRepository<Sheet> _sheetRepository;
        private readonly ISheetCommentService _sheetCommentService;
        private readonly ISheetCommentRepository _sheetCommentRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<SheetService> _logger;

        public SheetService(
            IRepository<Sheet> sheetRepository, 
            ISheetCommentService sheetCommentService,
            ISheetCommentRepository sheetCommentRepository,
            IMapper mapper, 
            ILogger<SheetService> logger)
        {
            _sheetRepository = sheetRepository;
            _sheetCommentService = sheetCommentService;
            _sheetCommentRepository = sheetCommentRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<IEnumerable<SheetDto>> GetAllAsync()
        {
            var sheets = await _sheetRepository.GetAllAsync(trackChanges: false);
            return _mapper.Map<IEnumerable<SheetDto>>(sheets);
        }

        public async Task<SheetDto> GetByIdAsync(Guid id)
        {
            var sheet = await _sheetRepository.GetByIdAsync(id, trackChanges: false);
            return _mapper.Map<SheetDto>(sheet);
        }

        public async Task<IEnumerable<SheetDto>> GetByFlowIdAsync(Guid flowId)
        {
            var sheets = await _sheetRepository.GetAsync(s => s.FlowId == flowId, trackChanges: false);
            return _mapper.Map<IEnumerable<SheetDto>>(sheets);
        }

        public async Task<SheetDto> CreateAsync(SheetCreateDto sheetCreateDto)
        {
            var sheet = _mapper.Map<Sheet>(sheetCreateDto);
            var createdSheet = await _sheetRepository.AddAsync(sheet);
            return await GetByIdAsync(createdSheet.Id);
        }

        public async Task<bool> UpdateAsync(Guid id, SheetDto sheetDto)
        {
            var existingSheet = await _sheetRepository.GetByIdAsync(id, trackChanges: true);
            if (existingSheet == null) return false;

            _mapper.Map(sheetDto, existingSheet);
            await _sheetRepository.UpdateAsync(existingSheet);
            return true;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            return await _sheetRepository.DeleteAsync(id);
        }

        public async Task<string> GetContentAsync(Guid id)
        {
            var sheet = await _sheetRepository.GetByIdAsync(id, trackChanges: false);
            return sheet?.Content ?? string.Empty;
        }

        /// <inheritdoc/>
        public async Task<SheetUpdateWithCommentsResponseDto> UpdateContentWithCommentsAsync(Guid id, SheetUpdateWithCommentsDto updateDto)
        {
            var response = new SheetUpdateWithCommentsResponseDto();
            
            try
            {
                // 1. Aggiorna il contenuto dello sheet
                var existingSheet = await _sheetRepository.GetByIdAsync(id, trackChanges: true);
                if (existingSheet == null)
                {
                    response.Success = false;
                    response.ErrorMessage = "Sheet non trovato";
                    return response;
                }

                // 2. Mappa per risolvere gli ID temporanei
                var temporaryIdMap = new Dictionary<string, Guid>();

                // 3. Prima passata: crea i commenti parent (quelli senza ParentTemporaryId)
                var parentOperations = updateDto.CommentOperations
                    .Where(op => op.Operation == CommentOperationType.Create && 
                                (string.IsNullOrEmpty(op.ParentTemporaryId) || op.ParentTemporaryId == "") && 
                                !op.ParentCommentId.HasValue)
                    .ToList();

                var createdComments = new List<SheetComment>();

                foreach (var operation in parentOperations)
                {
                    // Crea l'entità
                    var newComment = new SheetComment
                    {
                        Id = Guid.NewGuid(),
                        SheetId = id,
                        RefId = operation.RefId,
                        Comment = operation.Comment,
                        ParentCommentId = null,
                        FlowStatus = updateDto.FlowStatus
                    };

                    createdComments.Add(newComment);
                    temporaryIdMap[operation.TemporaryId] = newComment.Id;
                    
                    await _sheetCommentRepository.AddAsync(newComment);
                }

                // 4. Seconda passata: crea i commenti child (risposte)
                var childOperations = updateDto.CommentOperations
                    .Where(op => op.Operation == CommentOperationType.Create && 
                                ((!string.IsNullOrEmpty(op.ParentTemporaryId) && op.ParentTemporaryId != "") || op.ParentCommentId.HasValue))
                    .ToList();

                foreach (var operation in childOperations)
                {
                    Guid? parentId = null;
                    
                    if (!string.IsNullOrEmpty(operation.ParentTemporaryId) && operation.ParentTemporaryId != "")
                    {
                        // Parent è un commento pending - usa la mappa
                        if (temporaryIdMap.TryGetValue(operation.ParentTemporaryId, out var mappedParentId))
                        {
                            parentId = mappedParentId;
                        }
                        else
                        {
                            throw new InvalidOperationException($"Parent temporaneo ID non trovato: {operation.ParentTemporaryId}");
                        }
                    }
                    else if (operation.ParentCommentId.HasValue)
                    {
                        // Parent è un commento esistente
                        parentId = operation.ParentCommentId.Value;
                    }

                    var newReply = new SheetComment
                    {
                        Id = Guid.NewGuid(),
                        SheetId = id,
                        RefId = operation.RefId,
                        Comment = operation.Comment,
                        ParentCommentId = parentId,
                        FlowStatus = updateDto.FlowStatus
                    };

                    if (!string.IsNullOrEmpty(operation.TemporaryId))
                    {
                        temporaryIdMap[operation.TemporaryId] = newReply.Id;
                    }

                    await _sheetCommentRepository.AddAsync(newReply);
                }

                // 5. Gestisci le operazioni di aggiornamento
                var updateOperations = updateDto.CommentOperations
                    .Where(op => op.Operation == CommentOperationType.Update)
                    .ToList();

                foreach (var operation in updateOperations)
                {
                    if (!operation.CommentId.HasValue)
                    {
                        throw new InvalidOperationException("CommentId è richiesto per le operazioni di aggiornamento");
                    }

                    var existingComment = await _sheetCommentRepository.GetByIdAsync(operation.CommentId.Value, trackChanges: true);
                    if (existingComment != null)
                    {
                        existingComment.Comment = operation.Comment;
                        existingComment.FlowStatus = updateDto.FlowStatus;
                        await _sheetCommentRepository.UpdateAsync(existingComment);
                    }
                }

                // 6. Gestisci le operazioni di eliminazione
                var deleteOperations = updateDto.CommentOperations
                    .Where(op => op.Operation == CommentOperationType.Delete)
                    .ToList();

                foreach (var operation in deleteOperations)
                {
                    if (!operation.CommentId.HasValue)
                    {
                        throw new InvalidOperationException("CommentId è richiesto per le operazioni di eliminazione");
                    }

                    var commentToDelete = await _sheetCommentRepository.GetByIdAsync(operation.CommentId.Value, trackChanges: true);
                    if (commentToDelete != null)
                    {
                        await _sheetCommentRepository.DeleteAsync(commentToDelete);
                    }
                }

                // 7. Aggiorna il contenuto HTML sostituendo gli ID temporanei con i GUID reali
                string updatedContent = updateDto.Content;

                _logger.LogDebug($"Contenuto aggiornato: {updatedContent}");
                
                // Aggiorna il contenuto dello sheet con gli ID corretti
                existingSheet.Content = updatedContent;
                await _sheetRepository.UpdateAsync(existingSheet);

                response.Success = true;
                response.TemporaryIdMap = temporaryIdMap;
                
                _logger.LogInformation($"Sheet {id} aggiornato con successo con {updateDto.CommentOperations.Count} operazioni sui commenti");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Errore nell'aggiornamento dello sheet {id} con commenti");
                response.Success = false;
                response.ErrorMessage = ex.Message;
            }

            return response;
        }
        
        /// <inheritdoc/>
        public async Task<SheetCommentDto> CreateCommentWithContentUpdateAsync(Guid id, SheetCommentCreateDto commentDto, string currentContent)
        {
            try
            {
                // 1. Verifica che lo sheet esista
                var existingSheet = await _sheetRepository.GetByIdAsync(id, trackChanges: true);
                if (existingSheet == null)
                {
                    throw new InvalidOperationException($"Sheet con ID {id} non trovato");
                }

                // 2. Crea il commento utilizzando il servizio dei commenti
                var createdComment = await _sheetCommentService.CreateAsync(commentDto);
                if (createdComment == null)
                {
                    throw new InvalidOperationException("Errore nella creazione del commento");
                }

                // 3. Il contenuto HTML è già corretto con il refId, non serve fare sostituzioni
                _logger.LogInformation($"Commento creato con refId: {commentDto.RefId}");
                _logger.LogDebug($"Contenuto HTML: {currentContent}");
                
                // 4. Aggiorna il contenuto dello sheet
                existingSheet.Content = currentContent;
                await _sheetRepository.UpdateAsync(existingSheet);
                
                _logger.LogInformation($"Sheet {id} aggiornato con successo con nuovo commento {commentDto.RefId}");
                
                return createdComment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Errore nella creazione del commento con aggiornamento contenuto per sheet {id}");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteCommentWithContentUpdateAsync(Guid id, Guid commentId, string currentContent)
        {
            try
            {
                // 1. Verifica che lo sheet esista
                var existingSheet = await _sheetRepository.GetByIdAsync(id, trackChanges: true);
                if (existingSheet == null)
                {
                    throw new InvalidOperationException($"Sheet con ID {id} non trovato");
                }

                // 2. Ottieni il commento da eliminare per sapere cosa rimuovere dal contenuto
                var commentToDelete = await _sheetCommentService.GetByIdAsync(commentId);
                if (commentToDelete == null)
                {
                    throw new InvalidOperationException($"Commento con ID {commentId} non trovato");
                }

                // 3. Elimina il commento utilizzando il servizio dei commenti
                var deleteSuccess = await _sheetCommentService.DeleteAsync(commentId);
                if (!deleteSuccess)
                {
                    throw new InvalidOperationException($"Errore nell'eliminazione del commento {commentId}");
                }

                // 4. Aggiorna il contenuto HTML rimuovendo l'evidenziazione del commento eliminato
                string updatedContent = RemoveCommentHighlightFromContent(currentContent, commentToDelete.RefId);
                
                _logger.LogInformation($"Rimozione evidenziazione per commento con refId: {commentToDelete.RefId}");

                // 5. Salva il contenuto aggiornato
                existingSheet.Content = updatedContent;
                await _sheetRepository.UpdateAsync(existingSheet);

                _logger.LogInformation($"Eliminazione atomica completata per commento {commentId} nello sheet {id}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Errore nell'eliminazione atomica del commento {commentId} nello sheet {id}");
                throw;
            }
        }
        
        /// <summary>
        /// Rimuove l'evidenziazione di un commento dal contenuto HTML
        /// </summary>
        private string RemoveCommentHighlightFromContent(string content, string refId)
        {
            if (string.IsNullOrEmpty(content) || string.IsNullOrEmpty(refId))
                return content;

            string updatedContent = content;

            // Pattern per trovare span con il refId specifico
            var spanPattern = $@"<span[^>]*data-ref-id=""{refId}""[^>]*>(.*?)</span>";

            // Prova prima con data-ref-id esatto
            var regex = new System.Text.RegularExpressions.Regex(spanPattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            var match = regex.Match(updatedContent);

            if (match.Success)
            {
                // Sostituisci lo span con il solo testo interno (senza tooltip)
                var innerContent = ExtractTextFromSpan(match.Value);
                updatedContent = regex.Replace(updatedContent, innerContent);
            }

            return updatedContent;
        }

        /// <summary>
        /// Estrae il testo puro da uno span eliminando i tag HTML interni (come tooltip)
        /// </summary>
        private string ExtractTextFromSpan(string spanHtml)
        {
            if (string.IsNullOrEmpty(spanHtml))
                return string.Empty;

            // Rimuovi i tag span di apertura e chiusura
            var content = System.Text.RegularExpressions.Regex.Replace(spanHtml, @"<span[^>]*>", "");
            content = System.Text.RegularExpressions.Regex.Replace(content, @"</span>", "");
            
            // Rimuovi eventuali tag tooltip interni
            content = System.Text.RegularExpressions.Regex.Replace(content, @"<span[^>]*class=""[^""]*tooltip[^""]*""[^>]*>.*?</span>", "");
            
            return content.Trim();
        }
    }
} 