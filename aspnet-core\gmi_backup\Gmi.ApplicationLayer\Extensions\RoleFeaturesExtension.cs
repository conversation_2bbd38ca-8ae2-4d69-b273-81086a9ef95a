﻿using Gmi.DomainModelLayer.Entities;
using Gmi.DomainModelLayer.ValueObjects;

namespace Gmi.ApplicationLayer.Extensions
{
    public static class RoleFeaturesExtension
    {
        public static IEnumerable<(RoleType RoleType, IEnumerable<RoleFeature?> Features)> GroupByRoleTypes(this IEnumerable<RoleFeature> allRoleFeatures, IEnumerable<RoleType> allRoleTypes)
        {
            return allRoleTypes
                .GroupJoin(
                    allRoleFeatures,
                    roleType => roleType,
                    roleFeature => roleFeature.RoleId,
                    (roleType, roleFeatures) => (RoleType: roleType, RoleFeatures: roleFeatures
                    ))
                .SelectMany(
                    group => group.RoleFeatures.DefaultIfEmpty(),
                    (group, roleFeature) => (group.RoleType, RoleFeatures: roleFeature))
                .GroupBy(item => item.RoleType, item => item.RoleFeatures,
                    (key, roleFeatures) => (
                    RoleType: key,
                    Features: roleFeatures.Where(roleFeature => roleFeature != null)));
        }


    }
}
