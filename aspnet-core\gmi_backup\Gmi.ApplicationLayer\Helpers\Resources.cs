﻿using System.Reflection;
using System.Text;

namespace Gmi.ApplicationLayer.Helpers
{
    public static class Resources
    {
        public static class Embedded
        {
            public static string ItEquipmentsCreatedNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentsCreatedNotificationTemplate.html");
            public static string ItEquipmentsApprovedNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentsApprovedNotificationTemplate.html");
            public static string ItEquipmentsNotificationVerifiedTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentsNotificationVerifiedTemplate.html");
            public static string ItEquipmentsNotificationDemotedTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentsNotificationDemotedTemplate.html");
            public static string ItEquipmentsNotificationRestoredTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentsNotificationRestoredTemplate.html");
            public static string MalfunctioningItEquipmentNotificationTemplate => GetResource("Resources.HtmlTemplates.MalfunctioningItEquipmentNotificationTemplate.html");
            public static string ItEquipmentLocallyRepairedNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentLocallyRepairedNotificationTemplate.html");
            public static string ItEquipmentUnderRepairOTNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentUnderRepairOTNotificationTemplate.html");
            public static string ItEquipmentUnderRepairNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentUnderRepairNotificationTemplate.html");
            public static string ItEquipmentPickedUpNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentPickedUpNotificationTemplate.html");
            public static string ItEquipmentInstalledNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentInstalledNotificationTemplate.html");
            public static string ItEquipmentReportedOutOfOrderNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentReportedOutOfOrderNotificationTemplate.html");
            public static string ItEquipmentOutOfOrderNotificationTemplate => GetResource("Resources.HtmlTemplates.ItEquipmentOutOfOrderNotificationTemplate.html");
            public static string RoleRequestApprovedNotificationTemplate => GetResource("Resources.HtmlTemplates.RoleRequestApprovedNotificationTemplate.html");
            public static string RoleRequestRejectedNotificationTemplate => GetResource("Resources.HtmlTemplates.RoleRequestRejectedNotificationTemplate.html");
            public static string RoleRequestedNotificationTemplate => GetResource("Resources.HtmlTemplates.RoleRequestedNotificationTemplate.html");
            public static string OrderDetailsApprovedNotificationTemplate => GetResource("Resources.HtmlTemplates.OrderDetailsApprovedNotificationTemplate.html");
            public static string InstitutionCensusStartedNotificationTemplate => GetResource("Resources.HtmlTemplates.InstitutionCensusStartedNotificationTemplate.html");
            public static string InstitutionCensusCompletedNotificationTemplate => GetResource("Resources.HtmlTemplates.InstitutionCensusCompletedNotificationTemplate.html");
            public static string WarehouseCensusStartedNotificationTemplate => GetResource("Resources.HtmlTemplates.WarehouseCensusStartedNotificationTemplate.html");
            public static string WarehouseCensusCompletedNotificationTemplate => GetResource("Resources.HtmlTemplates.WarehouseCensusCompletedNotificationTemplate.html");
            public static string MovementOrderCreatedNotificationTemplate => GetResource("Resources.HtmlTemplates.MovementOrderCreatedNotificationTemplate.html");
            public static string MovementOrderUpdatedNotificationTemplate => GetResource("Resources.HtmlTemplates.MovementOrderUpdatedNotificationTemplate.html");
            public static string MovementOrderSignedNotificationTemplate => GetResource("Resources.HtmlTemplates.MovementOrderSignedNotificationTemplate.html");
            public static string MovementOrderFinalizedNotificationTemplate => GetResource("Resources.HtmlTemplates.MovementOrderFinalizedNotificationTemplate.html");

            public static string ItEquipmentsCreatedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "ItEquipmentsCreatedNotificationTitle");
            public static string ItEquipmentsApprovedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "ItEquipmentsApprovedNotificationTitle");
            public static string ItEquipmentsNotificationVerifiedTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "ItEquipmentsNotificationVerifiedTitle");
            public static string ItEquipmentsNotificationDemotedTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "ItEquipmentsNotificationDemotedTitle");
            public static string ItEquipmentsNotificationRestoredTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "ItEquipmentsNotificationRestoredTitle");
            public static string RoleRequestApprovedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "RoleRequestApprovedNotificationTitle");
            public static string RoleRequestRejectedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "RoleRequestRejectedNotificationTitle");
            public static string RoleRequestedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "RoleRequestedNotificationTitle");
            public static string OrderDetailApprovedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "OrderDetailsApprovedNotificationTitle");
            public static string InstitutionCensusStartedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "InstitutionCensusStartedNotificationTitle");
            public static string InstitutionCensusCompletedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "InstitutionCensusCompletedNotificationTitle");
            public static string WarehouseCensusStartedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "WarehouseCensusStartedNotificationTitle");
            public static string WarehouseCensusCompletedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "WarehouseCensusCompletedNotificationTitle");
            public static string MovementOrderCreatedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "MovementOrderCreatedNotificationTitle");
            public static string MovementOrderSignedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "MovementOrderSignedNotificationTitle");
            public static string MovementOrderUpdatedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "MovementOrderUpdatedNotificationTitle");
            public static string MovementOrderFinalizedNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "MovementOrderFinalizedNotificationTitle");
            public static string MalfunctioningItEquipmentNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "MalfunctioningItEquipmentNotificationTitle");
            public static string LocallyRepairedItEquipmentNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "LocallyRepairedItEquipmentNotificationTitle");
            public static string PickedUpItEquipmentNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "PickedUpItEquipmentNotificationTitle");
            public static string UnderRepairOTItEquipmentNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "UnderRepairOTItEquipmentNotificationTitle");
            public static string UnderRepairItEquipmentNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "UnderRepairItEquipmentNotificationTitle");
            public static string InstalledItEquipmentNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "InstalledItEquipmentNotificationTitle");
            public static string ReportedOutOfOrderItEquipmentNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "ReportedOutOfOrderItEquipmentNotificationTitle");
            public static string OutOfOrderItEquipmentNotificationTitle => GetStringResource("Resources.Strings.NotificationTitles.txt", "OutOfOrderItEquipmentNotificationTitle");

            public static Stream MovementOrderDocument => GetManifestResourceStream("Resources.Documents.ordine di trasferimento_modello_v2.docx");
            public static Stream MovementOrderMissiveInstitutionToInstitution => GetManifestResourceStream("Resources.Documents.ordine di trasferimento_modello_v2 -E-E.docx");
            public static Stream MovementOrderMissiveInstitutionToInstitutionWithMotra => GetManifestResourceStream("Resources.Documents.ordine di trasferimento_modello_v2.docx");
            public static Stream MovementOrderMissiveWarehouseToWarehouse => GetManifestResourceStream("Resources.Documents.ordine di trasferimento_modello_v2 -E-E.docx");
            public static Stream MovementOrderMissiveWarehouseToWarehouseWithMotra => GetManifestResourceStream("Resources.Documents.ordine di trasferimento_modello_v2.docx");
            public static Stream MovementOrderMissiveWarehouseToInstitution => GetManifestResourceStream("Resources.Documents.assegnazione_modello_v2-M-E.docx");
            public static Stream MovementOrderMissiveWarehouseToInstitutionWithMotra => GetManifestResourceStream("Resources.Documents.assegnazione_modello_v2-M-E_con_Motra");
            public static Stream MovementOrderMissiveInstitutionToWarehouse => GetManifestResourceStream("Resources.Documents.retrocessione_modello_v2-E-M");
            public static Stream ManagementOrderDocument => GetManifestResourceStream("Resources.Documents.ordine gestionale.docx");
            public static Stream MalfunctionRAEEMissive => GetManifestResourceStream("Resources.Documents.doc_fuoriuso.docx");


            private static string GetResource(string resourcePath)
            {
                using Stream stream = GetManifestResourceStream(resourcePath);
                using var streamReader = new StreamReader(stream, Encoding.UTF8);
                return streamReader.ReadToEnd();
            }

            private static string GetStringResource(string resourcePath, string name)
            {
                using Stream stream = GetManifestResourceStream(resourcePath);
                using StreamReader reader = new StreamReader(stream);
                Dictionary<string, string> resources = [];
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    string[] parts = line.Split(['='], 2);
                    if (parts.Length > 0)
                    {
                        string key = parts[0].Trim();
                        string value = parts.Length > 1 ? parts[1].Trim() : string.Empty;
                        resources[key] = value;
                    }
                }
                return resources[name];
            }

            private static Stream GetManifestResourceStream(string resourcePath)
            {
                var assembly = Assembly.GetExecutingAssembly();
                return assembly
                    .GetManifestResourceStream($"{assembly.GetName().Name}.{resourcePath}")!;
            }
        }
    }
}
