﻿using AutoMapper;
using Gmi.ApplicationLayer.Models.Request;
using Gmi.DomainModelLayer.Entities;
using Gmi.InfrastructureLayer.Data;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Collections;
using System.Reflection;

namespace Gmi.ApplicationLayer.Filters
{
    public class ValidateMaterialRequestModelAttribute : ActionFilterAttribute
    {

        private GmiDbContext _dbContext;
        private IMapper _mapper;

        public override void OnActionExecuting(ActionExecutingContext actionExecutingContext)
        {

            _dbContext = actionExecutingContext.HttpContext.RequestServices.GetRequiredService<GmiDbContext>();
            _mapper = actionExecutingContext.HttpContext.RequestServices.GetRequiredService<IMapper>();
            foreach (var materialRequestModel in actionExecutingContext.ActionArguments.Where(arg=> arg.Value is MaterialRequestModel))
            {
                var materialModel = _mapper.Map<Material>(materialRequestModel.Value as MaterialRequestModel);
                if (materialModel.ModelId == 0)
                {
                    actionExecutingContext.ModelState.AddModelError(nameof(Material.ModelId), $"{nameof(Material.ModelId)} is required");
                }
                else
                {
                    var forbiddenProperties = GetForbiddenPropertiesForModel(materialModel.ModelId);
                    ValidateModelForType(materialModel, forbiddenProperties, actionExecutingContext);
                }
            }
            base.OnActionExecuting(actionExecutingContext);
        }

        private void ValidateModelForType(Material materialModel, List<string> forbiddenProperties, ActionExecutingContext actionExecutingContext)
        {
            var allMaterialProperties = typeof(Material).GetProperties(BindingFlags.Instance | BindingFlags.Public);
            foreach (var forbiddenProperty in forbiddenProperties)
            {
                var property = allMaterialProperties.Single(mp => mp.Name == forbiddenProperty);
                var propertyValue = property.GetValue(materialModel);

                if (propertyValue is IEnumerable enumerable && enumerable.Cast<object>().Any() || propertyValue != null)
                {
                    actionExecutingContext.ModelState.AddModelError(
                        forbiddenProperty,
                        $"{forbiddenProperty} is forbidden when {nameof(Material.ModelId)} has value {materialModel.ModelId}"
                    );
                }
            }
        }

        private List<string> GetForbiddenPropertiesForModel(int modelId)
        {
            var model = _dbContext
                .Models
                .Include(m => m.ItemType)
                    .ThenInclude(it => it.MaterialForbiddenProperties)
                    .ThenInclude(mp => mp.MaterialProperty)
                .Single(m => m.ModelId == modelId);

            return model.ItemType.MaterialForbiddenProperties.Select(p => p.MaterialProperty.Name).ToList();
        }
    }
}
