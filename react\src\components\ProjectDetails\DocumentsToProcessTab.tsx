import { useNavigate } from 'react-router-dom';
import { FlowDto } from '../../api/models';
import styles from './DocumentsToProcessTab.module.css';
import { formatDate } from '../../utils/dateUtils';
import { getFlowStatusLabel, getFlowStatusStyles } from '../../utils/flowStatusUtils';
import { FlowStatus } from '../../api/enums';
import { HiOutlineLightBulb, HiOutlineUser, HiOutlineDocumentText } from 'react-icons/hi';

interface Props {
  documents: FlowDto[];
}

export const DocumentsToProcessTab = ({ documents }: Props) => {
  const navigate = useNavigate();

  const handleDocumentClick = (documentId: string) => {
    navigate(`/flow/${documentId}`);
  };

  if (!documents || documents.length === 0) {
    return (
      <div className={styles.emptyState}>
        <HiOutlineDocumentText className={styles.emptyIcon} />
        <h3 className={styles.emptyTitle}>Nessun documento da trattare</h3>
        <p className={styles.emptyDescription}>
          Non ci sono documenti in attesa di elaborazione per questo progetto.
        </p>
      </div>
    );
  }

  // --- Sorting Logic --- 
  const sortedDocuments = [...documents]
    .map((doc) => ({
      ...doc,
      needsAction: doc.flowStatus === FlowStatus._1 || doc.flowStatus === FlowStatus._2,
      parsedCreationTime: new Date(doc.creationTime || 0)
    }))
    .sort((a, b) => {
      if (a.needsAction && !b.needsAction) return -1;
      if (!a.needsAction && b.needsAction) return 1;
      return b.parsedCreationTime.getTime() - a.parsedCreationTime.getTime();
    });
  // --- End Sorting Logic ---

  return (
    <div className={styles.container}>
      <div className={styles.documentsGrid}>
        {sortedDocuments.map((document) => (
          <div
            key={document.id}
            className={styles.documentCard}
            onClick={() => handleDocumentClick(document.id!)}
          >
            <div className={styles.documentHeader}>
              <h3 className={styles.documentTitle}>
                <HiOutlineLightBulb className={styles.icon} /> <span className={styles.label}>Oggetto</span> <span className={styles.value}>{document.subject}</span>
                {document.needsAction && <span className={styles.actionRequiredBadge} title="Azione richiesta"></span>}
              </h3>
              <span className={styles.documentDate}>
                {formatDate(document.creationTime)} 
              </span>
            </div>
            <div className={styles.documentDepartment}>
              <HiOutlineUser className={styles.icon} />
              <span className={styles.departmentCode}>{document.creatorRank} {document.creatorFullName}</span>
               <span className={styles.departmentName}>{document.organizationUnitName}</span>
            </div>
            <div className={styles.documentFooter}>
              <span className={styles.documentType}>
                {document.flowType?.name || 'Tipo non specificato'}
              </span>
              <span 
                className={styles.documentStatus}
                style={getFlowStatusStyles(document.flowStatus as FlowStatus)}
              >
                {getFlowStatusLabel(document.flowStatus as FlowStatus)}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 