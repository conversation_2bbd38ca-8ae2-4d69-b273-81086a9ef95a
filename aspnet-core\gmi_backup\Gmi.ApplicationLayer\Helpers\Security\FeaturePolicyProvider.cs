﻿using Gmi.DomainModelLayer.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;

namespace Gmi.ApplicationLayer.Helpers.Security
{
    public class FeaturePolicyProvider(IOptions<AuthorizationOptions> options) : IAuthorizationPolicyProvider
    {
        const string POLICY_PREFIX = "Feature";
        public DefaultAuthorizationPolicyProvider FallbackPolicyProvider { get; } = new DefaultAuthorizationPolicyProvider(options);

        public Task<AuthorizationPolicy> GetDefaultPolicyAsync() => FallbackPolicyProvider.GetDefaultPolicyAsync();

        public Task<AuthorizationPolicy?> GetFallbackPolicyAsync() => FallbackPolicyProvider.GetFallbackPolicyAsync();

        public Task<AuthorizationPolicy> GetPolicyAsync(string policyName)
        {
            if (policyName.StartsWith(POLICY_PREFIX, StringComparison.OrdinalIgnoreCase))
            {
                var policy = new AuthorizationPolicyBuilder();
                policy.AddRequirements(new FeatureRequirement(
                    policyName[POLICY_PREFIX.Length..]
                    ?.Split('|', StringSplitOptions.RemoveEmptyEntries)
                    ?.Select(s => (Features)Enum.Parse(typeof(Features), s)) ?? [])
                    );
                return Task.FromResult(policy.Build());
            }

            return FallbackPolicyProvider.GetPolicyAsync(policyName);
        }
    }
}
