import React, { useState, useEffect } from 'react';
import { Modal } from '../Modal/Modal';
import { Button } from '../Buttons/Button';
import { UserDto } from '../../api/models';
import { HiOutlineSearch, HiPlus, HiX } from 'react-icons/hi';
import styles from './ContributorsModal.module.css';
import { FlowUserService } from '../../api/services/FlowUserService';
import { FlowUserRoleType } from '../../api/enums';
import { toast } from 'react-toastify';
import { FlowService } from '../../api/services/FlowService';
import { ProjectService } from '../../api/services/ProjectService';

interface ContributorsModalProps {
  isOpen: boolean;
  onClose: () => void;
  flowId: string;
  onSave?: () => void;
}

export const ContributorsModal: React.FC<ContributorsModalProps> = ({
  isOpen,
  onClose,
  flowId,
  onSave
}) => {
  const [contributors, setContributors] = useState<UserDto[]>([]);
  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);
  const [currentFlowUsers, setCurrentFlowUsers] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadContributors();
    }
  }, [isOpen, flowId]);

  const loadContributors = async () => {
    try {
      setLoading(true);
      
      // Carica il flusso per ottenere l'ID del progetto
      const flow = await FlowService.getById(flowId);
      if (!flow?.project?.id) {
        toast.error('Impossibile caricare le informazioni del progetto.', {
          position: 'bottom-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        return;
      }

      // Carica i contributori attuali del flusso e gli utenti disponibili del progetto
      const [currentFlowUsersData, projectUsers] = await Promise.all([
        FlowUserService.getByFlow(flowId),
        ProjectService.getProjectUsers(flow.project.id)
      ]);
      
      // Salva i dati completi dei FlowUser per accedere ad acknowledge
      setCurrentFlowUsers(currentFlowUsersData);
      
      // Filtra solo i contributori (FlowUserRoleType.Contributor = 1)
      const contributorFlowUsers = currentFlowUsersData.filter((fu: any) => fu.role === FlowUserRoleType._1);
      
      // Estrai gli UserDto dai FlowUserDto
      const currentContributors = contributorFlowUsers
        .map((flowUser: any) => projectUsers.find(user => user.id === flowUser.userId))
        .filter(user => user !== undefined) as UserDto[];
      
      setContributors(currentContributors);
      
      // Filtra gli utenti disponibili per mostrare solo quelli attivi
      const filteredUsers = projectUsers.filter(user => user.isActive !== false);
      setAvailableUsers(filteredUsers);
      
    } catch (error) {
      console.error('Error loading contributors:', error);
      toast.error('Errore nel caricamento dei contributori. Riprova più tardi.', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredAvailableUsers = searchTerm.length >= 3 
    ? availableUsers.filter(user => 
        !contributors.some(contributor => contributor.id === user.id) &&
        (user.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.rank?.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.organizationUnit?.name?.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : [];

  const handleAddContributor = (user: UserDto) => {
    setContributors(prev => [...prev, user]);
  };

  const handleRemoveContributor = (userId: string) => {
    setContributors(prev => prev.filter(contributor => contributor.id !== userId));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // Utilizza il metodo generato automaticamente dal proxy
      const contributorIds = contributors.map(c => c.id!);
      await FlowUserService.updateContributors(flowId, contributorIds);
      
      toast.success('Contributori salvati con successo!', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      
      if (onSave) {
        onSave();
      }
      onClose();
    } catch (error) {
      console.error('Error saving contributors:', error);
      toast.error('Errore nel salvataggio dei contributori. Riprova più tardi.', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Gestione Contributori"
    >
      <div className={styles.modalContent}>
        {loading ? (
          <div className={styles.loading}>Caricamento contributori...</div>
        ) : (
          <>
            {/* Contributori attuali */}
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Contributori Attuali</h3>
              {contributors.length === 0 ? (
                <div className={styles.emptyState}>
                  Nessun contributore assegnato
                </div>
              ) : (
                <div className={styles.contributorsList}>
                  {contributors.map(contributor => {
                    // Trova il FlowUserDto corrispondente per ottenere lo stato di acknowledge
                    const flowUser = currentFlowUsers.find((fu: any) => fu.userId === contributor.id);
                    const isAcknowledged = flowUser?.acknowledge === true;
                    const isSavedContributor = flowUser !== undefined; // Esiste nel database
                    
                    return (
                      <div key={contributor.id} className={styles.contributorCard}>
                        <div className={styles.contributorInfo}>
                          <div className={styles.contributorHeader}>
                            <div className={styles.contributorName}>
                              {contributor.rank} {contributor.firstName} {contributor.lastName}
                            </div>
                            <div className={styles.contributorStatus}>
                              {isSavedContributor ? (
                                isAcknowledged ? (
                                  <span className={styles.statusBadgeCompleted}>
                                    ✅ Contribuzione completata
                                  </span>
                                ) : (
                                  <span className={styles.statusBadgeActive}>
                                    🔄 In contribuzione
                                  </span>
                                )
                              ) : (
                                <span className={styles.statusBadgeNew}>
                                  ➕ Da salvare
                                </span>
                              )}
                            </div>
                          </div>
                          <div className={styles.contributorDetails}>
                            <div className={styles.contributorEmail}>{contributor.email}</div>
                            <div className={styles.contributorUnit}>{contributor.organizationUnit?.name || 'N/A'}</div>
                            {isAcknowledged && flowUser?.acknowledgeDate && (
                              <div className={styles.acknowledgeDate}>
                                Completata il: {new Date(flowUser.acknowledgeDate).toLocaleDateString('it-IT')}
                              </div>
                            )}
                          </div>
                        </div>
                        {!isSavedContributor && (
                          <Button
                            type="error"
                            onClick={() => handleRemoveContributor(contributor.id!)}
                            className={styles.removeButton}
                          >
                            <HiX />
                          </Button>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Ricerca e aggiunta nuovi contributori */}
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Aggiungi Contributori</h3>
              
              <div className={styles.searchContainer}>
                <div className={styles.searchInputWrapper}>
                  <HiOutlineSearch className={styles.searchIcon} />
                  <input
                    type="text"
                    placeholder="Cerca per nome, email, grado o unità..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={styles.searchInput}
                  />
                </div>
              </div>

              <div className={styles.availableUsersList}>
                {searchTerm.length < 3 ? (
                  <div className={styles.emptyState}>
                    Digita almeno 3 caratteri per cercare contributori
                  </div>
                ) : filteredAvailableUsers.length === 0 ? (
                  <div className={styles.emptyState}>
                    Nessun utente trovato per la ricerca
                  </div>
                ) : (
                  filteredAvailableUsers.map(user => (
                    <div key={user.id} className={styles.userCard}>
                      <div className={styles.userInfo}>
                        <div className={styles.userName}>
                          {user.rank} {user.firstName} {user.lastName}
                        </div>
                        <div className={styles.userDetails}>
                          <div className={styles.userEmail}>{user.email}</div>
                          <div className={styles.userUnit}>{user.organizationUnit?.name || 'N/A'}</div>
                        </div>
                      </div>
                      <Button
                        type="primary"
                        onClick={() => handleAddContributor(user)}
                        className={styles.addButton}
                      >
                        <HiPlus />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Azioni */}
            <div className={styles.modalActions}>
              <Button
                type="secondary"
                onClick={onClose}
                disabled={saving}
              >
                Annulla
              </Button>
              <Button
                type="primary"
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? 'Salvataggio...' : 'Salva Contributori'}
              </Button>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
}; 