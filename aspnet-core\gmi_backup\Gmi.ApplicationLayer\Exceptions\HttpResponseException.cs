﻿using System.Net;

namespace Gmi.ApplicationLayer.Exceptions
{
    public class HttpResponseException : Exception
    {
        public int Status { get; private set; }

        public HttpResponseException(string message, int status) : base(message)
        {
            Status = status;
        }

        public HttpResponseException(HttpStatusCode httpStatusCode)
        {
            Status = (int)httpStatusCode;
        }

        public HttpResponseException(HttpStatusCode httpStatusCode, string message) : base(message)
        {
            Status = (int)httpStatusCode;
        }

        public HttpResponseException(int httpStatusCode, string message, Exception inner) : base(message, inner)
        {
            Status = httpStatusCode;
        }

        public HttpResponseException(HttpStatusCode httpStatusCode, string message, Exception inner) : base(message, inner)
        {
            Status = (int)httpStatusCode;
        }
    }
}
