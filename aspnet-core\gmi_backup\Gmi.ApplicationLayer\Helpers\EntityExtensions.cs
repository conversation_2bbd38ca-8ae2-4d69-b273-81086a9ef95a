﻿using Gmi.DomainModelLayer.Entities;

namespace Gmi.ApplicationLayer.Helpers
{
    public static class EntityExtensions
    {
        public static bool IsUnderWarranty(this ItEquipment itEquipment)
        {
            var detail = itEquipment?.Item?.OrderDetail;
            if (detail == null) return false;

            var date = CalculateWarranty(detail);

            return date >= DateTime.Now;
        }

        public static DateTime? CalculateWarranty(this OrderDetail detail) => detail.TookCharge && detail.TookChargeDate.HasValue ? detail.TookChargeDate.Value.AddDays(detail.WarrantyDays).AddDays(detail.ExtensionWarrantyDays) : null;
    }
}
