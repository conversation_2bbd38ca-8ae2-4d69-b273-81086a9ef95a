import { CanExecuteActionDto, FlowAvailableActionsDto, FlowCreateDto, FlowDto } from '../models';
import { FlowClient } from '../proxy';
import { httpClient } from '../httpClient';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7079';

/**
 * Service wrapper per FlowClient
 * Fornisce metodi statici per interagire con le API
 * Generato automaticamente - Non modificare manualmente
 */
export class FlowService {
  private static flowClient = new FlowClient(
    API_BASE_URL,
    httpClient()
  );

  /**
   * getAll - Generato automaticamente
   * @returns Promise che risolve in FlowDto[]
   */
  static getAll(signal?: AbortSignal): Promise<FlowDto[]> {
    return this.flowClient.getAll(signal);
  }

  /**
   * create - Generato automaticamente
   * @returns Promise che risolve in FlowDto
   */
  static create(body: FlowCreateDto | undefined, signal?: AbortSignal): Promise<FlowDto> {
    return this.flowClient.create(body, signal);
  }

  /**
   * getById - Generato automaticamente
   * @returns Promise che risolve in FlowDto
   */
  static getById(id: string, signal?: AbortSignal): Promise<FlowDto> {
    return this.flowClient.getById(id, signal);
  }

  /**
   * update - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static update(id: string, body: FlowDto | undefined, signal?: AbortSignal): Promise<void> {
    return this.flowClient.update(id, body, signal);
  }

  /**
   * delete - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static delete(id: string, signal?: AbortSignal): Promise<void> {
    return this.flowClient.delete(id, signal);
  }

  /**
   * getByFlowType - Generato automaticamente
   * @returns Promise che risolve in FlowDto[]
   */
  static getByFlowType(flowTypeId: string, signal?: AbortSignal): Promise<FlowDto[]> {
    return this.flowClient.getByFlowType(flowTypeId, signal);
  }

  /**
   * getByOrganizationUnit - Generato automaticamente
   * @returns Promise che risolve in FlowDto[]
   */
  static getByOrganizationUnit(organizationUnitId: string, signal?: AbortSignal): Promise<FlowDto[]> {
    return this.flowClient.getByOrganizationUnit(organizationUnitId, signal);
  }

  /**
   * getSheetsByFlowId - Generato automaticamente
   * @returns Promise che risolve in any[]
   */
  static getSheetsByFlowId(flowId: string, signal?: AbortSignal): Promise<any[]> {
    return this.flowClient.getSheetsByFlowId(flowId, signal);
  }

  /**
   * getAvailableActions - Generato automaticamente
   * @returns Promise che risolve in FlowAvailableActionsDto
   */
  static getAvailableActions(flowId: string, userId: string | undefined, signal?: AbortSignal): Promise<FlowAvailableActionsDto> {
    return this.flowClient.getAvailableActions(flowId, userId, signal);
  }

  /**
   * canExecuteAction - Generato automaticamente
   * @returns Promise che risolve in CanExecuteActionDto
   */
  static canExecuteAction(flowId: string, action: string | undefined, userId: string | undefined, signal?: AbortSignal): Promise<CanExecuteActionDto> {
    return this.flowClient.canExecuteAction(flowId, action, userId, signal);
  }

  /**
   * publishDraft - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static publishDraft(id: string, signal?: AbortSignal): Promise<void> {
    return this.flowClient.publishDraft(id, signal);
  }

  /**
   * sendForPreApproval - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static sendForPreApproval(id: string, signal?: AbortSignal): Promise<void> {
    return this.flowClient.sendForPreApproval(id, signal);
  }
}
