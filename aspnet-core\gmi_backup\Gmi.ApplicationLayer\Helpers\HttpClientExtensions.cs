﻿using System.Net.Http.Json;

namespace Gmi.ApplicationLayer.Helpers
{
    public static class HttpClientExtensions
    {
        public static async Task<HttpResponseMessage> GetAsync(this HttpClient client, string requestUri, (string Key, string Value)[] headers)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, requestUri);
            foreach (var (Key, Value) in headers)
            {
                request.Headers.Add(Key, Value);
            }
            return await client.SendAsync(request);
        }

        public static async Task<HttpResponseMessage> PatchAsync(this HttpClient client, string requestUri, JsonContent content, (string Key, string Value)[] headers)
        {
            var request = new HttpRequestMessage(HttpMethod.Patch, requestUri)
            {
                Content = content,
            };
            foreach (var (Key, Value) in headers)
            {
                request.Headers.Add(Key, Value);
            }
            return await client.SendAsync(request);
        }

        public static async Task<HttpResponseMessage> PostAsync(this HttpClient client, string requestUri, HttpContent content, (string Key, string Value)[] headers)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, requestUri)
            {
                Content = content,
            };
            foreach (var (Key, Value) in headers)
            {
                request.Headers.Add(Key, Value);
            }
            return await client.SendAsync(request);
        }

    }
}
