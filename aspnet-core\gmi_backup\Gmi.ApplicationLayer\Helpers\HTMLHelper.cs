﻿using System.Text;

namespace Gmi.ApplicationLayer.Helpers
{
    public static class HTMLHelper
    {
        public static class Table
        {
            #region PRIVATE METHODS
            private static StringBuilder OpenHeader(StringBuilder sb) => sb.AppendLine("<th>");
            private static StringBuilder CloseHeader(StringBuilder sb) => sb.AppendLine("</th>");
            private static StringBuilder OpenRow(StringBuilder sb) => sb.AppendLine("<tr>");
            private static StringBuilder CloseRow(StringBuilder sb) => sb.AppendLine("</tr>");
            private static StringBuilder OpenCell(StringBuilder sb) => sb.AppendLine("<td>");
            private static StringBuilder CloseCell(StringBuilder sb) => sb.AppendLine("</td>");
            private static StringBuilder AddInner(StringBuilder sb, string content) => sb.AppendLine(content);
            #endregion

            public static StringBuilder CreateHeaderRow(StringBuilder sb, List<string> columns)
            {
                OpenRow(sb);
                foreach (var column in columns)
                {
                    OpenHeader(sb);
                    AddInner(sb, column);
                    CloseHeader(sb); 
                }
                CloseRow(sb);

                return sb;
            }
            public static StringBuilder CreateRecordRow(StringBuilder sb, List<string> rows)
            {
                OpenRow(sb);
                foreach (var row in rows)
                {
                    OpenCell(sb);
                    AddInner(sb, row);
                    CloseCell(sb); 
                }
                CloseRow(sb);

                return sb;
            }
        }
    }
}
