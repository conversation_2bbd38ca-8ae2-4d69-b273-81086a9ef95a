﻿using Microsoft.AspNetCore.Http;
using System.Linq.Expressions;

namespace Gmi.ApplicationLayer.Helpers
{
    public static class HelpersExtensions
    {
        public static async Task ForEachAsync<T>(this IEnumerable<T> list, Func<T, Task> func)
        {
            foreach (var value in list)
            {
                await func(value);
            }
        }

        public static IQueryable<TSource> WhereIf<TSource>(this IQueryable<TSource> source, bool condition, Expression<Func<TSource, bool>> predicate)
        {
            if (condition)
                return source.Where(predicate);
            else
                return source;
        }

        public static bool CheckMimeType(this IFormFile file)
        {
            string extension = Path.GetExtension(file.FileName);
            _mappings.TryGetValue(extension, out string realMimeType);

            if (realMimeType == file.ContentType)
                return true;

            return false;
        }

        private static IDictionary<string, string> _mappings = new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase) {
            {".doc", "application/msword"},
            {".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
            {".pdf", "application/pdf"},
            {".ppt", "application/vnd.ms-powerpoint"},
            {".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"},
            {".xls", "application/vnd.ms-excel"},
            {".xlsm", "application/vnd.ms-excel.sheet.macroEnabled.12" },
            {".xml", "text/xml" },
            {".html", "text/html" },
            {".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
            {".jpg", "image/jpeg" },
            {".png", "image/png" },
            {".csv", "text/csv" }
        };
    }
}
